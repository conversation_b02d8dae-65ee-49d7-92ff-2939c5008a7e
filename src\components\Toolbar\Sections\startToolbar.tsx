import * as ToolbarPrimitive from "@radix-ui/react-toolbar"
import React from "react"
import { TooltipButton } from "../../common/TooltipButton"

import {
  AlignCenterIcon,
  AlignLeftIcon,
  AlignRightIcon,
  BoldIcon,
  ClearformatIcon,
  HyperlinkIcon,
  ItalicIcon,
  PackupIcon,
  PictureIcon,
  RedoIcon,
  RootThemeIcon,
  SameLevelIcon,
  SearchIcon,
  StyleBackgroundIcon,
  StyleBorderIcon,
  StyleCanvasIcon,
  StyleStructureIcon,
  SubThemeIcon,
  SummaryIcon,
  UndoIcon,
  UnfoldIcon,
  WatermarkIcon,
} from "../icons"
import type { ToolbarSectionProps } from "../types"
import { ToolbarColor, ToolbarSelect, ToolbarSeparator } from "../ui"

export const StartToolbar: React.FC<ToolbarSectionProps> = ({
  selectedNodeId,
  selectedNodeStyle,
  onAddChildNode,
  onToggleBold,
  onToggleItalic,
  onColorChange,
  onFontFamilyChange,
  onFontSizeChange,
  onTextAlignChange,
}) => {
  const isEnabled = selectedNodeId !== null

  return (
    <ToolbarPrimitive.Root className="toolbar" aria-label="开始工具栏">
      {/* 查找和替换 */}
      <TooltipButton
        label="查找替换"
        disabled={!isEnabled}
        className="toolbar-text-btn"
      >
        <SearchIcon />
        <span>查找替换</span>
      </TooltipButton>

      <ToolbarSeparator />

      {/* 撤销 */}
      <TooltipButton label="撤销" disabled={!isEnabled}>
        <UndoIcon />
      </TooltipButton>

      {/* 恢复 */}
      <TooltipButton label="恢复" disabled={!isEnabled}>
        <RedoIcon />
      </TooltipButton>

      <ToolbarSeparator />

      {/* 清除格式 */}
      <TooltipButton label="清除格式" disabled={!isEnabled}>
        <ClearformatIcon />
      </TooltipButton>

      <ToolbarSeparator />

      {/* 字体选择 */}
      <ToolbarSelect
        label="字体"
        options={["微软雅黑", "宋体", "Arial", "Times New Roman"]}
        value={selectedNodeStyle?.fontFamily || "微软雅黑"}
        onChange={onFontFamilyChange || (() => {})}
        disabled={!isEnabled}
        className="toolbar-font-select"
      />

      {/* 字号选择 */}
      <ToolbarSelect
        label="字号"
        options={["12", "14", "16", "18", "20", "24", "28", "32"]}
        value={(selectedNodeStyle?.fontSize?.toString() as string) || "14"}
        onChange={(val) =>
          onFontSizeChange && onFontSizeChange(parseInt(val, 10))
        }
        disabled={!isEnabled}
        className="toolbar-size-select"
      />

      <ToolbarSeparator />
      {/* 加粗 */}
      <TooltipButton
        label="加粗"
        onClick={onToggleBold}
        disabled={!isEnabled}
        active={selectedNodeStyle?.fontWeight === "bold"}
      >
        <BoldIcon />
      </TooltipButton>
      {/* 斜体 */}
      <TooltipButton
        label="斜体"
        onClick={onToggleItalic}
        disabled={!isEnabled}
        active={selectedNodeStyle?.fontStyle === "italic"}
      >
        <ItalicIcon />
      </TooltipButton>
      {/* 颜色选择 */}
      <ToolbarColor
        label="字体颜色"
        value={selectedNodeStyle?.color || "#000000"}
        onChange={onColorChange || (() => {})}
        disabled={!isEnabled}
      />

      <ToolbarSeparator />

      {/* 左对齐 */}
      <TooltipButton
        label="左对齐"
        onClick={() => onTextAlignChange && onTextAlignChange("left")}
        disabled={!isEnabled}
        active={selectedNodeStyle?.textAlign === "left"}
      >
        <AlignLeftIcon />
      </TooltipButton>
      {/* 居中对齐 */}
      <TooltipButton
        label="居中对齐"
        onClick={() => onTextAlignChange && onTextAlignChange("center")}
        disabled={!isEnabled}
        active={selectedNodeStyle?.textAlign === "center"}
      >
        <AlignCenterIcon />
      </TooltipButton>
      {/* 右对齐 */}
      <TooltipButton
        label="右对齐"
        onClick={() => onTextAlignChange && onTextAlignChange("right")}
        disabled={!isEnabled}
        active={selectedNodeStyle?.textAlign === "right"}
      >
        <AlignRightIcon />
      </TooltipButton>

      <ToolbarSeparator />

      {/* 子主题 */}
      <TooltipButton
        label="子主题"
        onClick={onAddChildNode}
        disabled={!isEnabled}
      >
        <SubThemeIcon />
      </TooltipButton>

      {/* 同级主题 */}
      <TooltipButton label="同级主题" disabled={!isEnabled}>
        <SameLevelIcon />
      </TooltipButton>

      <TooltipButton label="父主题" disabled={!isEnabled}>
        <RootThemeIcon />
      </TooltipButton>

      <ToolbarSeparator />

      <TooltipButton label="概要" disabled={!isEnabled}>
        <SummaryIcon />
      </TooltipButton>

      <TooltipButton label="外框" disabled={!isEnabled}>
        <StyleBorderIcon />
      </TooltipButton>
      <TooltipButton label="图片" disabled={!isEnabled}>
        <PictureIcon />
      </TooltipButton>
      <TooltipButton label="超链接" disabled={!isEnabled}>
        <HyperlinkIcon />
      </TooltipButton>
      <TooltipButton label="水印" disabled={!isEnabled}>
        <WatermarkIcon />
      </TooltipButton>
      <ToolbarSeparator />

      <TooltipButton label="画布" disabled={!isEnabled}>
        <StyleCanvasIcon />
      </TooltipButton>
      <TooltipButton label="风格" disabled={!isEnabled}>
        <StyleBackgroundIcon />
      </TooltipButton>
      <TooltipButton label="结构" disabled={!isEnabled}>
        <StyleStructureIcon />
      </TooltipButton>
      <ToolbarSeparator />
      <TooltipButton label="收起" disabled={!isEnabled}>
        <PackupIcon />
      </TooltipButton>

      <TooltipButton label="展开" disabled={!isEnabled}>
        <UnfoldIcon />
      </TooltipButton>
    </ToolbarPrimitive.Root>
  )
}
