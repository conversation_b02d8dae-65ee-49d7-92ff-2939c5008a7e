import * as Toolbar from "@radix-ui/react-toolbar"
import React from "react"
import { TooltipButton } from "../../common/TooltipButton"
import {
  StyleBackgroundIcon,
  StyleBorderIcon,
  StyleCanvasIcon,
  StyleClearIcon,
  StyleNodeStyleIcon,
  StyleShapeIcon,
  StyleStructureIcon,
} from "../icons"
import { StyleBorderColorIcon } from "../icons/styleToolbar/StyleBorderColor"
import { StyleLineColorIcon } from "../icons/styleToolbar/StyleLineColorIcon"
import { StyleLineIcon } from "../icons/styleToolbar/StyleLineIcon"
import { StyleLineWidthIcon } from "../icons/styleToolbar/StyleLineWidthIcon"
import { StyleNodeIcon } from "../icons/styleToolbar/StyleNodeIcon"

import type { ToolbarSectionProps } from "../types"
import "./index.css"
export const StyleToolbar: React.FC<ToolbarSectionProps> = ({
  selectedNodeId,
  selectedNodeStyle,
  onBorderWidthChange,
}) => {
  const isEnabled = selectedNodeId !== null

  return (
    <Toolbar.Root className="toolbar" aria-label="样式工具栏">
      <TooltipButton
        label="节点样式"
        disabled={!isEnabled}
        className="toolbar-text-btn"
      >
        <StyleNodeIcon />
        <span>节点样式</span>
      </TooltipButton>

      <TooltipButton label="形状" disabled={!isEnabled}>
        <StyleShapeIcon />
        <span>形状</span>
      </TooltipButton>

      <TooltipButton label="节点背景" disabled={!isEnabled}>
        <StyleBackgroundIcon />
        <span>节点背景</span>
      </TooltipButton>

      <Toolbar.Separator className="toolbar-separator-vertical" />

      <TooltipButton label="连线类型" disabled={!isEnabled}>
        <StyleLineIcon />
        <span>连线类型</span>
      </TooltipButton>

      <TooltipButton label="连线颜色" disabled={!isEnabled}>
        <StyleLineColorIcon />
        <span>连线颜色</span>
      </TooltipButton>

      <TooltipButton label="连线宽度" disabled={!isEnabled}>
        <StyleLineWidthIcon />
        <span>连线宽度</span>
      </TooltipButton>

      <Toolbar.Separator className="toolbar-separator-vertical" />

      {/* 边框宽度选择 */}
      <div className="toolbar-border-width-group">
        <TooltipButton label="边框宽度" disabled={!isEnabled}>
          <span>边框宽度</span>
        </TooltipButton>
        <select
          className="toolbar-border-width-select"
          value={(selectedNodeStyle?.borderWidth?.toString() as string) || "1"}
          onChange={(e) =>
            onBorderWidthChange &&
            onBorderWidthChange(parseInt(e.target.value, 10))
          }
          disabled={!isEnabled}
        >
          {["0", "1", "2", "3", "4", "5"].map((opt) => (
            <option key={opt} value={opt}>
              {opt}
            </option>
          ))}
        </select>
      </div>

      <TooltipButton label="边框颜色" disabled={!isEnabled}>
        <StyleBorderColorIcon />
        <span> 边框颜色</span>
      </TooltipButton>

      <TooltipButton label="边框类型" disabled={!isEnabled}>
        <StyleBorderIcon />
        <span>边框类型</span>
      </TooltipButton>

      <Toolbar.Separator className="toolbar-separator-vertical" />

      <TooltipButton label="清除样式" disabled={!isEnabled}>
        <StyleClearIcon />
        <span>清除样式</span>
      </TooltipButton>

      <Toolbar.Separator className="toolbar-separator-vertical" />

      <TooltipButton label="画布" disabled={!isEnabled}>
        <StyleCanvasIcon />
        <span>画布</span>
      </TooltipButton>

      <TooltipButton label="风格" disabled={!isEnabled}>
        <StyleNodeStyleIcon />
        <span>风格</span>
      </TooltipButton>

      <TooltipButton label="结构" disabled={!isEnabled}>
        <StyleStructureIcon />
        <span>结构</span>
      </TooltipButton>
    </Toolbar.Root>
  )
}
