 
import { ChevronDownIcon } from "@radix-ui/react-icons"
import * as Popover from "@radix-ui/react-popover"
import * as Tooltip from "@radix-ui/react-tooltip"
import React from "react"
import { IconButton } from "../common/IconButton"
import {
  createFileActions,
  createFileOperations,
  createLocationOperations,
} from "./constants"
import { useFileActions, useFileState } from "./hooks"
import { getIconComponent } from "./iconUtils"
import type { FileControlsProps } from "./types"

// 专门用于 Popover 的图标按钮组件，支持 Tooltip
const PopoverIconButton = React.forwardRef<
  HTMLButtonElement,
  {
    icon: React.ComponentType<{ className?: string }>
    label: string
    onClick?: () => void
    className?: string
  }
>(({ icon: Icon, label, onClick, className = "" }, ref) => {
  const [showTooltip, setShowTooltip] = React.useState(false)
  const [isClicked, setIsClicked] = React.useState(false)

  const handleMouseEnter = () => {
    if (!isClicked) {
      setShowTooltip(true)
    }
  }

  const handleMouseLeave = () => {
    setShowTooltip(false)
  }

  const handleClick = () => {
    setIsClicked(true)
    setShowTooltip(false) // 点击时隐藏 tooltip
    onClick?.()

    // 短暂延迟后重置点击状态，允许 tooltip 再次显示
    setTimeout(() => setIsClicked(false), 100)
  }

  return (
    <Tooltip.Root open={showTooltip} onOpenChange={setShowTooltip}>
      <Tooltip.Trigger asChild>
        <button
          ref={ref}
          className={`icon-btn icon-btn-md ${className}`}
          onClick={handleClick}
          aria-label={label}
          type="button"
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
          onFocus={handleMouseEnter}
          onBlur={handleMouseLeave}
        >
          <Icon />
        </button>
      </Tooltip.Trigger>
      <Tooltip.Portal>
        <Tooltip.Content className="tooltip-content" side="bottom" align="center" sideOffset={5}>
          {label}
          <Tooltip.Arrow className="tooltip-arrow" />
        </Tooltip.Content>
      </Tooltip.Portal>
    </Tooltip.Root>
  )
})

export const FileControls: React.FC<FileControlsProps> = ({
  fileName: propFileName,
  // onFileNameChange, // 暂时未使用
}) => {
  const [popoverOpen, setPopoverOpen] = React.useState(false)
  const { fileName, toggleStar, location } = useFileState()
  const {
    handleHome,
    handleNew,
    handleReset,
    handleMenu,
    handleShare,
    handleTag,
    handleMove,
    handleAddShortcut,
  } = useFileActions()

  // 使用传入的文件名或默认文件名
  const displayFileName = propFileName || fileName

  // 创建配置
  const fileActions = createFileActions({
    handleHome,
    handleNew,
    handleReset,
    handleMenu,
    toggleStar,
  })

  
  const fileOperations = createFileOperations({
    handleShare,
    toggleStar,
    handleTag,
  })

  const locationOperations = createLocationOperations({
    handleMove,
    handleAddShortcut,
  })

  return (
    <div className="file-controls">
      {/* 基础文件操作按钮 */}
      {fileActions.map((action) => (
        <IconButton
          key={action.id}
          icon={action.icon as React.ComponentType<{ className?: string }>}
          label={action.label}
          onClick={action.onClick}
          disabled={action.disabled}
        />
      ))}

      <span className="file-name">{displayFileName}</span>

      {/* 文件操作弹出菜单 */}
      <Popover.Root
        open={popoverOpen}
        onOpenChange={(open) => {
          console.log("Popover open state:", open)
          setPopoverOpen(open)
        }}
      >
        <Popover.Trigger asChild>
          <PopoverIconButton
            icon={ChevronDownIcon}
            label="更多选项"
            className="file-popover-trigger"
            onClick={() => {
              console.log("PopoverIconButton clicked")
              setPopoverOpen(!popoverOpen)
            }}
          />
        </Popover.Trigger>
        <Popover.Portal>
          <Popover.Content
            className="popover-content"
            side="bottom"
            align="start"
            sideOffset={4}
          >
            <div className="card-panel">
              {/* 顶部卡片 - 文件信息和操作 */}
              <div className="card-top">
                <div className="card-top-file">
                  <div className="file-info">
                    <div className="file-name">{displayFileName}</div>
                    <div className="file-actions">
                      {fileOperations.map((operation, index) => (
                        <React.Fragment key={operation.id}>
                          <button
                            className="action-btn"
                            onClick={operation.onClick}
                          >
                            {React.createElement(
                              getIconComponent(operation.icon),
                              {
                                className: "action-icon",
                              }
                            )}
                            {operation.label}
                          </button>
                          {index < fileOperations.length - 1 && (
                            <div className="separator" />
                          )}
                        </React.Fragment>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* 底部卡片 - 位置信息和操作 */}
              <div className="card-bottom">
                <div className="location-info">
                  <div className="folder-icon">📁</div>
                  <div className="location-path">{location}</div>
                </div>
                <div className="location-actions">
                  {locationOperations.map((operation) => (
                    <button
                      key={operation.id}
                      className="location-btn"
                      onClick={operation.onClick}
                    >
                      {React.createElement(getIconComponent(operation.icon), {
                        className: "location-icon",
                      })}
                      {operation.label}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </Popover.Content>
        </Popover.Portal>
      </Popover.Root>
    </div>
  )
}
