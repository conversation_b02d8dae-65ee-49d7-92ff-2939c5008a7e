import * as ToolbarPrimitive from "@radix-ui/react-toolbar"
import React from "react"
import { ToolbarButton } from "../ui"

export const ExportToolbar: React.FC = () => {
  return (
    <ToolbarPrimitive.Root className="toolbar" aria-label="导出工具栏">
      <ToolbarButton label="导出为图片">图片</ToolbarButton>
      <ToolbarButton label="导出为PDF">PDF</ToolbarButton>
      <ToolbarButton label="导出大纲">大纲</ToolbarButton>
      <ToolbarButton label="导出为POS">POS</ToolbarButton>
      <ToolbarButton label="导出为Excel">Excel</ToolbarButton>
      <ToolbarButton label="导出为FreeMind">FreeMind</ToolbarButton>
      <ToolbarButton label="PPT大纲文档">PPT大纲文档</ToolbarButton>
      <ToolbarButton label="全部制作POS文件">全部制作POS文件</ToolbarButton>
    </ToolbarPrimitive.Root>
  )
}
